export enum UserRole {
  OWNER = "owner",
  SUPER_ADMIN = "super_admin",
  MEMBER = "member",
}

export enum UserStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
}

export type UserBaseParams = {
  organization_id?: number;
};

export type UserPayload = Partial<
  User & UserBaseParams & { status: UserStatus }
>;

export type User = {
  id: number;
  first_name: string | null;
  last_name: string | null;
  photo_url: string | null;
  email: string;
  contact: string | null;
  last_login_at: string | null;
  role: UserRole;
  created_at: string | null;
  status: UserStatus;
};
