import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APStakeholderMapping,
  APStakeholderMappingBaseData,
} from "@/features/account-plan/types/position-types";

export const updateStakeholderMapping = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APStakeholderMappingBaseData;
}): ApiResponse<APStakeholderMapping> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_STAKEHOLDER_MAPPING_DETAIL(accountId, id),
    data
  );
};

type UseUpdateStakeholderMappingOptions = {
  mutationConfig?: MutationConfig<typeof updateStakeholderMapping>;
};

export const useUpdateStakeholderMapping = ({
  mutationConfig,
}: UseUpdateStakeholderMappingOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateStakeholderMapping,
  });
};
