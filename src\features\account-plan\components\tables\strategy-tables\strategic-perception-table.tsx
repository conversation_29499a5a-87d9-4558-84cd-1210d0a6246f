import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  SelectCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { APTargetedPerceptionDevelopment } from "@/features/account-plan/types/strategy-types";
import { useUpdateTargetedPerceptionDevelopment } from "@/features/account-plan/api/strategy-apis/targeted-perception-development/update-targeted-perception-development";
import { useTargetedPerceptionDevelopmentList } from "@/features/account-plan/api/strategy-apis/targeted-perception-development/get-targeted-perception-development-list";
import { useStakeholderMappingList } from "@/features/account-plan/api/position-apis/stakeholder-mapping/get-stakeholder-mapping-list";

import { AccountTable } from "../base-table";

export const StrategicPerceptionTable = () => {
  const [tableData, setTableData] = useState<APTargetedPerceptionDevelopment[]>(
    []
  );

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { targetedPerceptionDevelopmentList } =
    useTargetedPerceptionDevelopmentList({
      accountId,
      params: {
        disable_pagination: true,
      },
    });
  const { stakeholderMappingList } = useStakeholderMappingList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const updateTargetedPerceptionDevelopment =
    useUpdateTargetedPerceptionDevelopment({});

  useEffect(() => {
    if (!targetedPerceptionDevelopmentList) return;

    const newTableData = targetedPerceptionDevelopmentList
      ?.slice(0, 2)
      .sort((a, b) => a.id - b.id)
      .map((v, idx) => ({
        idx,
        ...v,
      }));

    setTableData(newTableData);
  }, [targetedPerceptionDevelopmentList]);

  const onChangeData = useCallback(
    async (data: Partial<APTargetedPerceptionDevelopment>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        const stakeholderId = data.ap_stakeholder_mapping_item?.id;

        await updateTargetedPerceptionDevelopment.mutateAsync({
          accountId,
          id,
          data: {
            ...(!!stakeholderId
              ? {
                  ap_stakeholder_mapping_item_id: stakeholderId,
                }
              : data),
          },
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateTargetedPerceptionDevelopment]
  );

  const columns: ColumnDef<APTargetedPerceptionDevelopment>[] = useMemo(
    () => [
      {
        accessorKey: "action_target",
        size: 200,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <SelectCell
              className={`align-top ${isPreview ? "text-4xl" : "text-lg"}`}
              placeholder=""
              isPreview={isPreview}
              value={rowData.ap_stakeholder_mapping_item?.name}
              onChange={(name) => {
                const ap_stakeholder_mapping_item =
                  stakeholderMappingList?.find((v) => v.name === name);

                onChangeData({ ap_stakeholder_mapping_item }, rowData.id);
              }}
              options={
                stakeholderMappingList
                  ?.map((v) => ({
                    label: v.name ?? "",
                    value: v.name ?? "",
                  }))
                  .filter((v) => v.value) ?? []
              }
            />
          );
        },
      },
      {
        accessorKey: "action",
        size: 300,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              className={`min-h-[12.5vh] ${isPreview ? "text-4xl" : "text-lg"}`}
              value={rowData.action}
              onChange={(action) => onChangeData({ action }, rowData.id)}
              placeholder="Perceived as the partner who helps them solve..."
            />
          );
        },
      },

      {
        accessorKey: "result",
        size: 300,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              className={`min-h-[12.5vh] ${isPreview ? "text-4xl" : "text-lg"}`}
              value={rowData.result}
              onChange={(result) => onChangeData({ result }, rowData.id)}
              placeholder="Which will help them achieve... (Result)"
            />
          );
        },
      },
      {
        accessorKey: "leverage",
        size: 300,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              className={`min-h-[12.5vh] ${isPreview ? "text-4xl" : "text-lg"}`}
              value={rowData.leverage}
              onChange={(leverage) => onChangeData({ leverage }, rowData.id)}
              placeholder="By leveraging our..."
            />
          );
        },
      },
    ],
    [onChangeData, stakeholderMappingList]
  );

  return (
    <AccountTable type={AccountPlanTableType.TARGETED_PERCEPTION_DEVELOPMENT}>
      <DataTable
        columns={columns}
        data={tableData}
        showHeader={false}
        tableType={AccountPlanTableType.TARGETED_PERCEPTION_DEVELOPMENT}
      />
    </AccountTable>
  );
};
