import {
  Toolt<PERSON>,
  Too<PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getCurrencyCode } from "@/constants/currencies";

export function TableTooltip({
  displayName,
  description,
  currency,
}: {
  displayName: string;
  description: string;
  currency?: string | null;
}) {
  const withCurrency = displayName === "Value";

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className="flex">
          {withCurrency ? `${getCurrencyCode(currency)} $` : displayName}{" "}
        </TooltipTrigger>
        <TooltipContent
          side="right"
          align="start"
          alignOffset={0}
          // fixed scale-150
          className="w-[30vw] flex-wrap whitespace-normal text-lg font-semibold"
        >
          {description}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
