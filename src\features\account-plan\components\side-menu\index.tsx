import { Separator } from "@/components/ui/separator";
import { useAccountPlanStore } from "../../stores";
import { cn } from "@/lib/utils";
import { AccountPlanTableType } from "../../types";
import { IconChevronRight } from "@tabler/icons-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getAccountPlanTableName } from "../../constants";

const sections = [
  {
    title: "POSITION",
    menu: [
      {
        name: getAccountPlanTableName(AccountPlanTableType.STAKEHOLDER_MAPPING),
        type: AccountPlanTableType.STAKEHOLDER_MAPPING,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.WALLET_SHARE),
        type: AccountPlanTableType.WALLET_SHARE,
      },
      {
        name: getAccountPlanTableName(
          AccountPlanTableType.CIRCUMSTANTIAL_ANALYSIS
        ),
        type: AccountPlanTableType.CIRCUMSTANTIAL_ANALYSIS,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.SVOT),
        type: AccountPlanTableType.SVOT,
      },
      {
        name: getAccountPlanTableName(
          AccountPlanTableType.INSIGHT_AND_PERSPECTIVE
        ),
        type: AccountPlanTableType.INSIGHT_AND_PERSPECTIVE,
      },
    ],
  },
  {
    title: "REVENUE",
    menu: [
      {
        name: getAccountPlanTableName(AccountPlanTableType.HISTORIC_REVENUE),
        type: AccountPlanTableType.HISTORIC_REVENUE,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.CURRENT_REVENUE),
        type: AccountPlanTableType.CURRENT_REVENUE,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.CURRENT_OPPORTUNITY),
        type: AccountPlanTableType.CURRENT_OPPORTUNITY,
      },
      {
        name: getAccountPlanTableName(
          AccountPlanTableType.POTENTIAL_OPPORTUNITY
        ),
        type: AccountPlanTableType.POTENTIAL_OPPORTUNITY,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.REVENUE_FORECAST),
        type: AccountPlanTableType.REVENUE_FORECAST,
      },
    ],
  },
  {
    title: "STRATEGY",
    menu: [
      {
        name: getAccountPlanTableName(AccountPlanTableType.MISSING_INFORMATION),
        type: AccountPlanTableType.MISSING_INFORMATION,
      },
      {
        name: getAccountPlanTableName(
          AccountPlanTableType.TARGETED_PERCEPTION_DEVELOPMENT
        ),
        type: AccountPlanTableType.TARGETED_PERCEPTION_DEVELOPMENT,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.ACTION_PLAN),
        type: AccountPlanTableType.ACTION_PLAN,
      },
      {
        name: getAccountPlanTableName(AccountPlanTableType.TOP_ACTION),
        type: AccountPlanTableType.TOP_ACTION,
      },
      {
        name: getAccountPlanTableName(
          AccountPlanTableType.CLIENT_MEETING_SCHEDULE
        ),
        type: AccountPlanTableType.CLIENT_MEETING_SCHEDULE,
      },
    ],
  },
];

export const AccountPlanSideMenu = () => {
  const showSidebar = useAccountPlanStore((state) => state.showSidebar);
  const toggleSidebar = useAccountPlanStore((state) => state.toggleSidebar);
  const toggleActiveTable = useAccountPlanStore(
    (state) => state.toggleActiveTable
  );

  return (
    <div className="relative z-10">
      <div
        className={cn(
          "-ml-res-x-base flex h-screen flex-col overflow-hidden rounded-r-[1vw] bg-gradient pl-res-x-sm text-white transition-all",
          showSidebar ? "w-[17vw] min-w-[300px]" : "w-0"
        )}
      >
        <div className="m-8 overflow-y-auto overflow-x-hidden pr-4 scrollbar-thin">
          {sections.map((v, idx) => (
            <div key={idx} className="b-8">
              <p className="mb-4 text-xs tracking-[.25em] text-neutral-300">
                {v.title}
              </p>
              <div className="flex flex-col gap-6">
                {v.menu.map((v, idx) => (
                  <p
                    key={idx}
                    onClick={() => toggleActiveTable(v.type)}
                    className="cursor-pointer text-nowrap text-sm font-light hover:text-neutral-200"
                  >
                    {v.name}
                  </p>
                ))}
              </div>
              {idx < sections.length - 1 && (
                <Separator className="my-8 w-4/5" />
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="absolute -right-res-x-lg top-1/2 -translate-y-1/2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <IconChevronRight
                className={cn(
                  "size-icon-res-lg cursor-pointer transition-all",
                  showSidebar && "rotate-180"
                )}
                onClick={toggleSidebar}
              />
            </TooltipTrigger>
            <TooltipContent side="bottom" align="start">
              {showSidebar ? "Hide" : "Show"} Table List
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};
