"use client";

import { HexColorPicker } from "react-colorful";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import _ from "lodash";
import Image from "next/image";

import PsAiLogo from "@/assets/ps-ai-logo.svg?url";
import { Input } from "@/components/ui/input";
import { LoginForm } from "@/features/landing-page/login-form";
import { Separator } from "@/components/ui/separator";
import { useUpdateOrganization } from "@/features/organizations/api/update-organization";
import { useOrganization } from "@/features/organizations/api/get-organization";
import {
  OrganizationData,
  OrganizationPayload,
} from "@/features/organizations/types";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/constants/query-keys";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { IconHelpCircle, IconPhoto } from "@tabler/icons-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { UploadButton } from "@/components/ui/upload-button";
import withProtectedRoute from "@/features/auth/components/protected-route";
import { UserRole } from "@/features/auth/types/user";
import { PUBLIC_ORGANIZATION_ID } from "@/features/organizations/constants";
import { OrganzationCrms } from "@/features/organizations/components/organization-crms";
import { useAuth } from "@/features/auth/api/get-auth";

function ColorPicker({
  color,
  children,
  onUpdate,
}: React.HTMLAttributes<HTMLDivElement> & {
  color: string;
  onUpdate: (color: string) => void;
}) {
  const [colorInput, setColorInput] = useState("");

  const onColorChange = (newColor: string) => {
    onUpdate(newColor);
    setColorInput(newColor);
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setColorInput(e.target.value);
  };

  const onInputBlur = () => {
    if (/^#[0-9A-F]{6}$/i.test(colorInput)) {
      onColorChange(colorInput);
    } else {
      setColorInput(color);
    }
  };

  useEffect(() => {
    if (!!color) {
      setColorInput(color);
    }
  }, [color]);

  return (
    <Popover>
      <PopoverTrigger>
        <div className="flex cursor-pointer items-center gap-res-x-sm">
          {!!color && (
            <div
              className={"size-res-x-sm rounded-full"}
              style={{ backgroundColor: color }}
            ></div>
          )}
          {children}
        </div>
      </PopoverTrigger>
      <PopoverContent className="flex flex-col items-center gap-res-y-base">
        <HexColorPicker onChange={onColorChange} color={color} />

        <div className="flex items-center space-x-2">
          <Input
            className="flex-grow"
            placeholder="#000000"
            value={colorInput}
            onChange={onInputChange}
            onBlur={onInputBlur}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
}

function SettingInput({
  label,
  tooltip,
  onUpdate,
  children,
  ...props
}: {
  tooltip?: string;
  onUpdate?: (value: string) => void;
  label: string;
  children?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>) {
  const [cursor, setCursor] = useState<number | null>(null);
  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    ref.current?.setSelectionRange(cursor, cursor);
  }, [ref, cursor, props.value]);

  return (
    <div className="flex items-center gap-res-x-base rounded-md border bg-white px-res-x-sm py-res-y-xs">
      <div className="flex">
        <p className="mr-res-x-xs w-[8vw]">{label} </p>:
      </div>
      {!!children ? (
        children
      ) : (
        <Input
          ref={ref}
          className="w-[25vw] border-b border-transparent border-b-slate-200 bg-transparent"
          onChange={(e) => {
            setCursor(e.target.selectionStart);
            onUpdate?.(e.target.value);
          }}
          {...props}
        />
      )}
      {!!tooltip && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <IconHelpCircle />
            </TooltipTrigger>
            <TooltipContent>{tooltip}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

function Settings() {
  const { isHydrated } = useIsHydrated();
  const updateOrganization = useUpdateOrganization({});
  const queryClient = useQueryClient();
  const { user } = useAuth({});

  const { organization } = useOrganization({
    organizationId: user?.organization?.id ?? PUBLIC_ORGANIZATION_ID,
  });

  const onUpdateDebounced = useMemo(
    () =>
      _.debounce(async (data: OrganizationPayload) => {
        try {
          await updateOrganization.mutateAsync({
            organizationId: PUBLIC_ORGANIZATION_ID,
            data,
          });
        } catch (_) {
          toast.error("An error occured while updating the data");
        }
      }, 1000),
    [updateOrganization]
  );

  const onUpdate = (data: OrganizationPayload) => {
    queryClient.setQueryData(
      [QUERY_KEYS.ORGANIZATION, PUBLIC_ORGANIZATION_ID],
      (old: { data: OrganizationData }) => {
        return {
          ...old,
          data: {
            ...old.data,
            ...data,
          },
        };
      }
    );

    onUpdateDebounced(data);
  };

  const baseColors = useMemo(
    () =>
      [
        {
          text: "Primary Colours",
          color: organization?.primary_color,
          field: "primary_color",
        },
        {
          text: "Primary (Light) Colours",
          color: organization?.primary_light,
          field: "primary_light",
        },
        {
          text: "Primary (Extra Light) Colours",
          color: organization?.primary_extra_light,
          field: "primary_extra_light",
        },
        {
          text: "Secondary Colours",
          color: organization?.secondary_color,
          field: "secondary_color",
        },
      ] as const,
    [organization]
  );

  const chartColors = useMemo(
    () =>
      [
        {
          text: "Existing",
          color: organization?.ap_current_revenue_color,
          field: "ap_wallet_share_color",
        },
        {
          text: "Competiton",
          color: organization?.ap_current_opportunity_color,
          field: "ap_current_opportunity_color",
        },
        {
          text: "Available",
          color: organization?.ap_potential_opportunity_color,
          field: "ap_potential_opportunity_color",
        },
      ] as const,
    [organization]
  );

  if (!isHydrated) return null;

  return (
    <div className="w-full">
      <h1 className="text-3xl font-bold text-primary-500">Settings</h1>
      <div className="grid grid-cols-[40vw_1fr]">
        <div className="mt-res-y-3xl w-full">
          <div className="gap flex w-full">
            <div className="my-auto mr-res-y-3xl flex flex-col items-center">
              <Avatar className="size-[12vw] rounded-md border border-neutral-300">
                <AvatarImage
                  className="rounded-none"
                  src={organization?.logo_url ?? ""}
                />
                <AvatarFallback className="rounded-none bg-neutral-200 p-res-x-sm">
                  <Image
                    src={PsAiLogo}
                    alt="ps-ai-logo"
                    className="h-full w-auto"
                  />
                </AvatarFallback>
              </Avatar>
              <UploadButton
                className="mt-res-y-base bg-gradient px-res-x-base"
                onChangeFile={(logo_url) => {
                  onUpdate({ logo_url });
                }}
              >
                Upload Company Logo
              </UploadButton>
            </div>

            <div className="ml-res-x-sm flex grow flex-col gap-res-y-base">
              <div className="grid w-full gap-res-y-sm rounded-md border border-neutral-300 bg-white p-res-x-sm">
                {baseColors.map((v, idx) => (
                  <ColorPicker
                    key={idx}
                    color={v.color ?? ""}
                    onUpdate={(color) => {
                      onUpdate({ [v.field]: color });
                    }}
                  >
                    {v.text}
                  </ColorPicker>
                ))}
              </div>
              <div className="grid gap-res-y-sm rounded-md border border-neutral-300 bg-white p-res-x-sm">
                {chartColors.map((v, idx) => (
                  <ColorPicker
                    key={idx}
                    color={v.color ?? ""}
                    onUpdate={(color) => {
                      onUpdate({ [v.field]: color });
                    }}
                  >
                    {v.text}
                  </ColorPicker>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-res-y-base grid w-fit gap-res-y-base">
            <SettingInput
              label="Company Name"
              value={organization?.name ?? ""}
              onUpdate={(name) => {
                onUpdate({ name });
              }}
            />
            <SettingInput
              label="Company Tagline"
              value={organization?.tagline ?? ""}
              onUpdate={(tagline) => {
                onUpdate({ tagline });
              }}
            />

            <div className="px-res-x-lg">
              <Separator className="h-[0.5vh] bg-gradient" />
            </div>

            <SettingInput
              label="Company Message"
              value={organization?.message ?? ""}
              onUpdate={(message) => {
                onUpdate({ message });
              }}
              tooltip="You can use backtick `` to highlight the text"
            />
            <SettingInput label="Company Image">
              <Avatar className="my-res-y-xs size-[12vh] rounded-sm">
                <AvatarImage src={organization?.image_url ?? ""} />
                <AvatarFallback className="size-full rounded-sm bg-neutral-200">
                  <IconPhoto className="size-full text-white" />{" "}
                </AvatarFallback>
              </Avatar>
              <UploadButton
                className="size-s my-res-y-xs px-res-x-sm"
                onChangeFile={(image_url) => {
                  onUpdate({ image_url });
                }}
              >
                Change Image
              </UploadButton>
            </SettingInput>

            <OrganzationCrms organization={organization!} />
          </div>
        </div>
        <div className="relative ml-[2.5vw]">
          <LoginForm className="mx-auto h-[90vh] w-[35vw] min-w-[10vw] overflow-hidden rounded-3xl text-white" />
          <div className="absolute left-0 top-0 h-full w-full"></div>
        </div>
      </div>
    </div>
  );
}

export default withProtectedRoute(Settings, UserRole.OWNER);
