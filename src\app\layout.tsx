import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import type { Metadata } from "next";
import "./globals.css";

import { QueryClientProvider } from "@/lib/react-query";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import RootContainer from "@/features/landing-page/root-container";

export const metadata: Metadata = {
  title: "Perception Selling",
  description: "Your AI solution",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <QueryClientProvider>
      <TooltipProvider>
        <RootContainer>
          <ReactQueryDevtools initialIsOpen />
          {children}
          <Toaster />
        </RootContainer>
      </TooltipProvider>
    </QueryClientProvider>
  );
}
