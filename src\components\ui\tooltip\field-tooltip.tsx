import { ReactNode } from "react";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  TooltipTrigger,
} from "../tooltip";
import { IconInfoCircle } from "@tabler/icons-react";

export function FieldTooltip({ children }: { children: ReactNode }) {
  return (
    <Tooltip delayDuration={50}>
      <TooltipTrigger
        asChild
        className="ml-1 h-4 w-4 cursor-pointer text-muted-foreground"
      >
        <IconInfoCircle />
      </TooltipTrigger>
      <TooltipContent className="max-w-[400px] font-normal">
        {children}
      </TooltipContent>
    </Tooltip>
  );
}
