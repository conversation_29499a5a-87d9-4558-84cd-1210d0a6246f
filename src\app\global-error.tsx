"use client";

import { useEffect } from "react";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Global Error:", error);
  }, [error]);

  return (
    <html>
      <body>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="max-w-md rounded-lg bg-white p-6 shadow-lg">
            <h2 className="mb-4 text-xl font-semibold text-red-600">
              Application Error
            </h2>
            <p className="mb-4 text-gray-600">
              A critical error occurred in the application.
            </p>
            {error.message.includes("Maximum update depth exceeded") && (
              <div className="mb-4 rounded bg-red-50 p-3">
                <p className="text-sm text-red-700">
                  <strong>Infinite Loop Detected:</strong> This error is typically
                  caused by components that trigger re-renders in an infinite loop.
                </p>
              </div>
            )}
            <div className="flex gap-2">
              <button
                onClick={reset}
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Try again
              </button>
              <button
                onClick={() => window.location.href = "/"}
                className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
              >
                Go Home
              </button>
            </div>
            {process.env.NODE_ENV === "development" && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Error details (development only)
                </summary>
                <pre className="mt-2 max-h-40 overflow-auto text-xs text-gray-600">
                  {error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      </body>
    </html>
  );
}
