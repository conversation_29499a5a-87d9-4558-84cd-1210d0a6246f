"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { IconAlertCircle, IconArrowLeft } from "@tabler/icons-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { isRequestError } from "@/lib/api-client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useForgotPassword } from "@/features/user-management/api/passwords/forgot-password";
import { PATH } from "@/constants/path";
import Link from "next/link";
import { useOrganization } from "../organizations/api/get-organization";
import { PUBLIC_ORGANIZATION_ID } from "../organizations/constants";
import { FormContainer } from "./form-base-layout";

const formSchema = z.object({
  email: z.string().email(),
});

type FormSchema = z.infer<typeof formSchema>;

function ForgotPasswordForm() {
  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
  });
  const forgotPassword = useForgotPassword({});
  const { secondaryColor } = useOrganization({
    organizationId: PUBLIC_ORGANIZATION_ID,
  });

  const [errorNotif, setErrorNotif] = useState<string>("");
  const [isSuccess, setIsSuccess] = useState(false);

  const onSubmit = async (data: FormSchema) => {
    try {
      setErrorNotif("");
      await forgotPassword.mutateAsync({ data });
      setIsSuccess(true);
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";

        setErrorNotif(errorMessage ?? "");
      }
    }
  };

  if (isSuccess) {
    return (
      <FormContainer>
        <div className="text-primary-500">
          <p className="mb-4">
            Check your email for a link to reset your password. If it doesn’t
            appear within a few minutes, check your spam folder.
          </p>
          <Link href={PATH.LANDING}>
            <Button
              className="mt-4 w-full"
              type="submit"
              style={{
                backgroundColor: secondaryColor,
              }}
            >
              <IconArrowLeft className="mr-2" /> Back to Home Page
            </Button>
          </Link>
        </div>
      </FormContainer>
    );
  }

  return (
    <Form {...form}>
      <FormContainer onSubmit={form.handleSubmit(onSubmit)}>
        <div>
          <div className="text-2xl text-primary-500">Forgot password</div>
        </div>

        <div className="mb-8 grid gap-8">
          {errorNotif && (
            <Alert variant="destructive" className="relative mb-2">
              <IconAlertCircle className="h-4 w-4" />
              <AlertTitle className="font-bold">
                Reset Password Failed
              </AlertTitle>
              <AlertDescription>{errorNotif}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      id="email"
                      type="email"
                      className="h-12"
                      placeholder="Enter your email address"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full"
              style={{
                backgroundColor: secondaryColor,
              }}
              isLoading={forgotPassword.isPending}
            >
              Send Email
            </Button>

            <Link href={PATH.LANDING} className="w-full">
              <Button variant="ghost" className="w-full text-primary-500">
                Back to Home Page
              </Button>
            </Link>
          </div>
        </div>
      </FormContainer>
    </Form>
  );
}

export default ForgotPasswordForm;
