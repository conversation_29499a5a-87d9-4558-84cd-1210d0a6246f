"use client";

import React, { useEffect, useRef, useState } from "react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AutoComplete } from "@/components/ui/autocomplete";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DatePicker } from "@/components/ui/date-picker";

type MetadataFieldProps = {
  title: string;
  tooltip?: string;
  disableEdit?: boolean;
  defaultValue?: string;
  onSave?: (value: string, helpers: { reset: () => void }) => void;
  required?: boolean;
  withWarning?: boolean;
};

const useConfirmationDialog = ({
  title,
  onConfirm,
}: {
  title: string;
  onConfirm: () => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [skipFocus, setSkipFocus] = useState(false);

  const handleFocus = () => {
    if (skipFocus) {
      setSkipFocus(false);
      return;
    }
    setIsOpen(true);
  };

  const handleProceed = () => {
    setIsOpen(false);
    setSkipFocus(true);
    onConfirm();
  };

  const ConfirmationDialog = () => (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title} Edit Notice</DialogTitle>
          <DialogDescription>
            Applying changes to the {title} will affect all versions of account
            plans sharing the same {title}. <br />
            Do you wish to proceed?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="ghost" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleProceed}>Proceed</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return { isOpen, handleFocus, ConfirmationDialog };
};

export const AccountMetadataAutocomplete = ({
  title,
  required,
  ...props
}: React.ComponentPropsWithoutRef<typeof AutoComplete> &
  MetadataFieldProps) => {
  const ref = useRef<HTMLInputElement>(null);

  const { handleFocus, ConfirmationDialog, isOpen } = useConfirmationDialog({
    title,
    onConfirm: () => {
      setTimeout(() => ref.current?.focus(), 0);
    },
  });

  const isError = required && ref.current?.value === "";

  return (
    <div>
      <ConfirmationDialog />
      <AutoComplete
        ref={ref}
        onBlur={() => {
          if (!ref.current?.value && !isOpen) {
            props.onValueChange?.({
              label: "",
              value: "",
            });
          }
        }}
        onFocus={handleFocus}
        {...props}
      />
      <p className="mt-res-y-xs h-[1vh] text-xs text-red-500">
        {isError ? <>{title} Input is missing</> : ""}
      </p>
    </div>
  );
};

export const AccountMetadataInput = ({
  title,
  defaultValue,
  onSave,
  required,
  titleProps, // eslint-disable-line @typescript-eslint/no-unused-vars
  withWarning = true,
  className,
  ...props
}: React.ComponentPropsWithoutRef<typeof Input> &
  MetadataFieldProps & { titleProps?: React.ComponentProps<"p"> }) => {
  const ref = useRef<HTMLInputElement>(null);

  const [value, setValue] = useState("");
  const { isOpen, handleFocus, ConfirmationDialog } = useConfirmationDialog({
    title,
    onConfirm: () => {
      setTimeout(() => {
        ref.current?.focus();
      }, 0);
    },
  });

  const isError = required && value === "";

  const handleBlur = () => {
    if (isOpen) return;

    onSave?.(value, {
      reset: () => setValue(defaultValue ?? ""),
    });
  };

  useEffect(() => {
    if (defaultValue) {
      setValue(defaultValue);
    }
  }, [defaultValue]);

  return (
    <div>
      {withWarning && <ConfirmationDialog />}
      <Input
        ref={ref}
        className={cn(
          "disabled:opacity-100",
          isError && "!border-red-500",
          className
        )}
        disabled={props.disabled}
        onFocus={withWarning ? handleFocus : () => {}}
        placeholder={title}
        onChange={(e) => setValue(e.target.value)}
        onBlur={handleBlur}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.currentTarget.blur();
          }
        }}
        {...props}
        value={value}
      />

      <p className="mt-res-y-xs h-[1vh] text-xs text-red-500">
        {isError ? <>{title} Input is missing</> : ""}
      </p>
    </div>
  );
};

export const AccountMetadataDateInput = ({
  title,
  defaultValue,
  onSave,
  tooltip, // eslint-disable-line @typescript-eslint/no-unused-vars
  required,
  withWarning = true,
  ...props
}: React.ComponentPropsWithoutRef<typeof DatePicker> &
  Omit<MetadataFieldProps, "defaultValue" | "onSave"> & {
    defaultValue: string | Date | null | undefined;
    onSave: (date: string | Date | null) => void;
  }) => {
  const [isOpenCalendar, setIsOpenCalendar] = useState(false);
  const [value, setValue] = useState<string | Date | null>(null);

  const isError = required && !value;

  const { handleFocus: handleFocusDialog, ConfirmationDialog } =
    useConfirmationDialog({
      title,
      onConfirm: () => {
        setIsOpenCalendar(true);
      },
    });

  useEffect(() => {
    if (defaultValue) {
      setValue(defaultValue ?? null);
    }
  }, [defaultValue]);

  return (
    <div>
      {withWarning && <ConfirmationDialog />}
      <DatePicker
        {...props}
        date={value}
        className={cn(
          "h-[4.5vh] w-full disabled:opacity-100",
          isError && "!border-red-500"
        )}
        open={isOpenCalendar}
        onOpenChange={(open) => {
          if (!withWarning) return setIsOpenCalendar(open);

          if (open) return handleFocusDialog();

          return setIsOpenCalendar(false);
        }}
        onSelect={(date) => {
          setValue(date ?? null);
          onSave(date ?? null);
        }}
        placeholder={title}
        icon={false}
        format={props.format ?? "DD/MM/YYYY"}
      />

      <p className="mt-res-y-xs h-[1vh] text-xs text-red-500">
        {isError ? <>{title} Input is missing</> : ""}
      </p>
    </div>
  );
};
