"use client";

import React from "react";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; errorInfo?: React.ErrorInfo }>;
}

class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error for debugging
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    // Check if it's the infinite loop error we're trying to fix
    if (error.message.includes("Maximum update depth exceeded")) {
      console.error("🚨 INFINITE LOOP DETECTED:", {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      });
    }

    this.setState({
      error,
      errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props;

      if (Fallback && this.state.error) {
        return (
          <Fallback error={this.state.error} errorInfo={this.state.errorInfo} />
        );
      }

      // Default fallback UI
      return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="max-w-md rounded-lg bg-white p-6 shadow-lg">
            <h2 className="mb-4 text-xl font-semibold text-red-600">
              Something went wrong
            </h2>
            <p className="mb-4 text-gray-600">
              An error occurred while rendering this component.
            </p>
            {this.state.error?.message.includes(
              "Maximum update depth exceeded"
            ) && (
              <div className="mb-4 rounded bg-red-50 p-3">
                <p className="text-sm text-red-700">
                  <strong>Infinite Loop Detected:</strong> This error is
                  typically caused by components that trigger re-renders in an
                  infinite loop.
                </p>
              </div>
            )}
            <button
              onClick={() =>
                this.setState({
                  hasError: false,
                  error: undefined,
                  errorInfo: undefined,
                })
              }
              className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              Try again
            </button>
            {process.env.NODE_ENV === "development" && this.state.error && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Error details (development only)
                </summary>
                <pre className="mt-2 max-h-40 overflow-auto text-xs text-gray-600">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
