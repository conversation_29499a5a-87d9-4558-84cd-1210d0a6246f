"use client";

import _ from "lodash";
import { produce } from "immer";
import React, { useMemo } from "react";
import { parseAsString, useQueryState } from "next-usequerystate";

import { PATH } from "@/constants/path";
import { Grid } from "@/components/ui/grid";
import { ColDef } from "ag-grid-community";
import { useModelList } from "@/features/model-templates/api/get-model-list";
import { useDeleteModel } from "@/features/model-templates/api/delete-model";
import {
  GridActions,
  GridDeleteButton,
} from "@/components/ui/grid/grid-actions";
import { StatusBadgeProps } from "@/components/ui/badge/status-badge";
import { useScalingDimension } from "@/lib/hooks/use-scaling-dimension";
import {
  ListTemplateCategory,
  ModelTemplate,
  ModelTemplateStatus,
} from "@/features/model-templates/types";
import { formatDate, getFullName } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import { useUpdateModelDetail } from "@/features/model-templates/api/update-model";
import { useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";

type TuningEngineData = ModelTemplate;

const filterOptions = [
  {
    value: "all",
    label: "Active Templates",
  },
  {
    value: ListTemplateCategory.CA_MACRO,
    label: "Circumstantial Analysis (Macro)",
  },
  {
    value: ListTemplateCategory.CA_BUSINESS,
    label: "Circumstantial Analysis (Business)",
  },
  {
    value: ListTemplateCategory.CA_INDUSTRY,
    label: "Circumstantial Analysis (Industry)",
  },
  {
    value: ListTemplateCategory.INSIGHT_AND_PERSPECTIVE,
    label: "Insights and Perspectives",
  },
] as const;

function DeleteModelButton({ modelId }: { modelId: number }) {
  const deleteModel = useDeleteModel({});

  const onDeleteModel = async () => {
    await deleteModel.mutateAsync({
      modelId,
    });
  };

  return (
    <GridDeleteButton
      onDelete={onDeleteModel}
      isLoading={deleteModel.isPending}
      itemName="model"
    />
  );
}

function TuningEngineList() {
  const [categoryFilter, setCategoryFilter] = useQueryState(
    "filter",
    parseAsString.withDefault(filterOptions[0].value)
  );

  const { getWidth, getHeight } = useScalingDimension();
  const queryClient = useQueryClient();
  const router = useRouter();

  const modelListParams = useMemo(() => {
    return categoryFilter === "all"
      ? { status: ModelTemplateStatus.ACTIVE }
      : {
          list_template_ap_category: categoryFilter as ListTemplateCategory,
        };
  }, [categoryFilter]);

  const { modelList, isFetching: isFetchingModelList } = useModelList({
    params: modelListParams,
  });
  const updateModelDetail = useUpdateModelDetail({});

  const tuningEngineColumns: ColDef<TuningEngineData>[] = useMemo(
    () => [
      {
        field: "name",
        headerName: "Version",
        maxWidth: getWidth(300),
      },
      {
        field: "status",
        maxWidth: getWidth(115),
        cellRenderer: (
          ...args: {
            data: { id: number };
            value: StatusBadgeProps["status"];
          }[]
        ) => {
          return (
            <Switch
              className="absolute left-1/2 -translate-x-1/2 disabled:opacity-100"
              disabled={args[0].value == ModelTemplateStatus.ACTIVE}
              checked={args[0].value == ModelTemplateStatus.ACTIVE}
              onCheckedChange={(value) => {
                const status = value
                  ? ModelTemplateStatus.ACTIVE
                  : ModelTemplateStatus.INACTIVE;

                updateModelDetail.mutate({
                  modelId: args[0].data.id,
                  data: {
                    status,
                  },
                });

                queryClient.setQueryData(
                  [QUERY_KEYS.MODEL_TEMPLATES, modelListParams],
                  (old: { data: ModelTemplate[] }) => {
                    const newData = produce(old?.data, (draft) => {
                      draft.forEach((v) => {
                        v.status =
                          v.id === args[0].data.id
                            ? ModelTemplateStatus.ACTIVE
                            : ModelTemplateStatus.INACTIVE;
                      });
                    });

                    return { data: newData };
                  }
                );
              }}
            />
          );
        },
      },
      {
        field: "last_updated_by",
        headerName: "Last Updated By",
        maxWidth: getWidth(300),
        valueFormatter: (params) =>
          getFullName(
            params?.data?.last_updated_by?.first_name,
            params?.data?.last_updated_by?.last_name
          ),
      },
      {
        field: "updated_at",
        headerName: "Last Saved",
        valueFormatter: (params) =>
          formatDate(params.data?.updated_at ?? "", "DD/MM/YYYY"),
        maxWidth: getWidth(150),
      },
      {
        field: "template_category.name",
        headerName: "Category",
      },
      {
        field: "id",
        headerName: "Action",
        maxWidth: getWidth(115),
        sortable: false,
        cellRenderer: (...args: { value: number }[]) => {
          return (
            <div className="absolute left-1/2 -translate-x-1/2">
              <GridActions>
                <DeleteModelButton modelId={args[0]?.value} />
              </GridActions>
            </div>
          );
        },
      },
    ],
    [getWidth, updateModelDetail, queryClient, modelListParams]
  );

  return (
    <div className="h-full w-full">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary-500">Tuning Engine</h1>
        <div className="flex items-center gap-4">
          <Link href={PATH.DASHBOARD_TUNING_ENGINE_CREATE}>
            <Button className="rounded-xl bg-gradient">Add Template</Button>
          </Link>
        </div>
      </div>

      <section className="mt-4 grid gap-8 rounded-xl bg-white p-8 text-primary-500">
        <Select
          onValueChange={(value) => setCategoryFilter(value)}
          value={categoryFilter ?? ""}
        >
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Select target table" />
          </SelectTrigger>
          <SelectContent>
            {filterOptions.map((v, idx) => (
              <SelectItem key={idx} value={v.value}>
                {v.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Grid<TuningEngineData>
          columnDefs={tuningEngineColumns}
          rowData={modelList}
          headerHeight={getHeight(60)}
          rowHeight={getHeight(60)}
          height="65vh"
          loading={isFetchingModelList}
          overlayNoRowsTemplate="No model found"
          autoSizeStrategy={{
            type: "fitGridWidth",
          }}
          onRowClicked={(e) => {
            if (
              !e.data?.id ||
              (e.eventPath?.[0] as Element) instanceof SVGElement ||
              (e.eventPath?.[0] as Element) instanceof HTMLButtonElement
            )
              return;

            router.push(PATH.DASHBOARD_TUNING_ENGINE_EDIT(e.data?.id));
          }}
          className="overflow-hidden rounded-lg"
        />
        <p className="ml-auto">
          Powered by TuneAI by{" "}
          <Link href="https://app.brandrev.ai/" className="font-bold">
            Brandrev.ai
          </Link>
        </p>
      </section>
    </div>
  );
}

export default TuningEngineList;
