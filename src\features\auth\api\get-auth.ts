import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { Auth } from "./login";
import { UserRole } from "../types/user";
import { useAuthStore } from "../stores/auth-store";
import { useCallback } from "react";

export const getAuth = (): ApiResponse<Auth> => {
  return api.get(API_ROUTES.AUTH);
};

export const getAuthOptions = () => {
  return queryOptions({
    queryKey: [QUERY_KEYS.AUTH_INFO],
    queryFn: () => getAuth(),
  });
};

type UseAuthOptions = {
  queryConfig?: QueryConfig<typeof getAuth>;
};

export const useAuth = ({ queryConfig }: UseAuthOptions) => {
  const { refreshToken } = useAuthStore();

  const authQuery = useQuery({
    ...getAuthOptions(),
    ...queryConfig,
    enabled: !!refreshToken,
  });
  const auth = authQuery.data?.data;

  const isOwnAccount = useCallback(
    (userId?: number) => auth?.user?.id === userId,
    [auth?.user?.id]
  );

  return {
    ...authQuery,
    auth,
    user: (auth?.user || {}) as Partial<Auth["user"]>,
    isOwnAccount,
    isOwner: auth?.user.role === UserRole.OWNER,
  };
};
