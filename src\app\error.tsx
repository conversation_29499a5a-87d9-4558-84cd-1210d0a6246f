"use client";

import { useEffect } from "react";
import { Button } from "@/components/ui/button";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("App Error:", error);
  }, [error]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="max-w-md rounded-lg bg-white p-6 shadow-lg">
        <h2 className="mb-4 text-xl font-semibold text-red-600">
          Something went wrong!
        </h2>
        <p className="mb-4 text-gray-600">
          An unexpected error occurred while loading this page.
        </p>
        {error.message.includes("Maximum update depth exceeded") && (
          <div className="mb-4 rounded bg-red-50 p-3">
            <p className="text-sm text-red-700">
              <strong>Infinite Loop Detected:</strong> This error is typically
              caused by components that trigger re-renders in an infinite loop.
            </p>
          </div>
        )}
        <div className="flex gap-2">
          <Button onClick={reset} variant="default">
            Try again
          </Button>
          <Button
            onClick={() => window.location.href = "/dashboard"}
            variant="outline"
          >
            Go to Dashboard
          </Button>
        </div>
        {process.env.NODE_ENV === "development" && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm text-gray-500">
              Error details (development only)
            </summary>
            <pre className="mt-2 max-h-40 overflow-auto text-xs text-gray-600">
              {error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}
