import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  DatePickerCell,
  SelectCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { Button } from "@/components/ui/button";
import { APClientMeetingSchedule } from "@/features/account-plan/types/strategy-types";
import { useClientMeetingScheduleList } from "@/features/account-plan/api/strategy-apis/client-meeting-schedule/get-client-meeting-schedule-list";
import { useUpdateClientMeetingSchedule } from "@/features/account-plan/api/strategy-apis/client-meeting-schedule/update-client-meeting-schedule";
import { useCreateClientMeetingSchedule } from "@/features/account-plan/api/strategy-apis/client-meeting-schedule/create-client-meeting-schedule";
import { useDeleteClientMeetingSchedule } from "@/features/account-plan/api/strategy-apis/client-meeting-schedule/delete-client-meeting-schedule";
import { useStakeholderMappingList } from "@/features/account-plan/api/position-apis/stakeholder-mapping/get-stakeholder-mapping-list";

import { AccountTable } from "../base-table";

export const MeetingScheduleTable = () => {
  const [tableData, setTableData] = useState<APClientMeetingSchedule[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { clientMeetingScheduleList } = useClientMeetingScheduleList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createClientMeetingSchedule = useCreateClientMeetingSchedule({});
  const updateClientMeetingSchedule = useUpdateClientMeetingSchedule({});
  const deleteClientMeetingSchedule = useDeleteClientMeetingSchedule({});
  const { stakeholderMappingList } = useStakeholderMappingList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  useEffect(() => {
    if (!clientMeetingScheduleList) return;

    const newTableData = clientMeetingScheduleList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [clientMeetingScheduleList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createClientMeetingSchedule.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteClientMeetingSchedule.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APClientMeetingSchedule>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        const stakeholderId = data.ap_stakeholder_mapping_item?.id;

        await updateClientMeetingSchedule.mutateAsync({
          accountId,
          id,
          data: {
            ...(!!stakeholderId
              ? {
                  ap_stakeholder_mapping_item_id: stakeholderId,
                }
              : data),
          },
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateClientMeetingSchedule]
  );

  const columns: ColumnDef<APClientMeetingSchedule>[] = useMemo(
    () => [
      {
        accessorKey: "meeting_date",
        header: "Date",
        size: 175,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <DatePickerCell
              className={isPreview ? "text-4xl" : "text-lg"}
              date={rowData.meeting_date}
              onSelect={(meeting_date) => {
                onChangeData(
                  { meeting_date: meeting_date as unknown as string },
                  rowData.id
                );
              }}
            />
          );
        },
        meta: {
          tooltip:
            "Specifies the scheduled date for the meeting with the client, helping ensure timely and organized engagement.",
          padding: true,
        },
      },
      {
        accessorKey: "client",
        header: "Meeting With",
        size: 225,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <SelectCell
              className={isPreview ? "text-4xl" : "text-lg"}
              isPreview={isPreview}
              value={rowData.ap_stakeholder_mapping_item?.name}
              onChange={(name) => {
                const ap_stakeholder_mapping_item =
                  stakeholderMappingList?.find((v) => v.name === name);

                onChangeData({ ap_stakeholder_mapping_item }, rowData.id);
              }}
              options={
                stakeholderMappingList
                  ?.map((v) => ({
                    label: v.name ?? "",
                    value: v.name ?? "",
                  }))
                  .filter((v) => !!v.value) ?? []
              }
            />
          );
        },
        meta: {
          tooltip:
            "Identifies the individual(s) or group(s) you will be meeting with, providing clarity on the participants involved in the discussion.",
          padding: true,
        },
      },
      {
        accessorKey: "notes",
        header: "Notes",
        size: 600,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.notes}
              onChange={(notes) => onChangeData({ notes }, rowData.id)}
              className={isPreview ? "text-4xl" : "text-lg"}
            />
          );
        },
        meta: {
          tooltip:
            "Additional notes or details about the meeting, including agenda items, objectives, or follow-up actions.",
          padding: true,
        },
      },
    ],
    [onChangeData, stakeholderMappingList]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.CLIENT_MEETING_SCHEDULE}
      footer={
        <>
          <Button
            onClick={onAddRow}
            isLoading={createClientMeetingSchedule.isPending}
          >
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
      heightRatio={0.2}
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        tableType={AccountPlanTableType.CLIENT_MEETING_SCHEDULE}
      />
    </AccountTable>
  );
};
